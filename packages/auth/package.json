{"name": "@convx/auth", "version": "0.0.0", "private": true, "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@aws-amplify/ui-react": "^6.11.2", "aws-amplify": "^6.15.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "typescript": "^5.5.3"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2"}}